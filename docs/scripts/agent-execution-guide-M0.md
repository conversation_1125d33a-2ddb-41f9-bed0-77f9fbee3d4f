# M0 Agent Execution Guide

## 🎯 Execution Overview
**Milestone**: M0 — Repository Skeleton & CI
**Confidence**: 93%
**Status**: READY FOR EXECUTION

## 🚀 Agent Execution Prompts

### 80% Standard Execution Pattern
Use these prompts for any milestone execution:

#### 1. Pre-Execution Setup
```bash
# Validate milestone readiness
node docs/scripts/milestone-analysis/analyze-milestone-core.mjs docs/tech-specs/milestones/milestone-M0.mdx

# Create feature branch
git checkout -b implement-M0
```

#### 2. Standard Project Structure Validation
**Agent Prompt**: "Verify the following directories exist and create them if missing:"
- `apps/` - For applications (api, web)
- `packages/` - For reusable packages and libraries
- `docs/tech-specs/` - For technical specifications
- `.github/workflows/` - For CI/CD workflows

#### 3. Package Configuration Setup
**Agent Prompt**: "Ensure the following scripts are available in package.json:"
- `build` - Build all packages and applications
- `test` - Run test suites
- `lint` - Run linting and code quality checks
- `dev:api` - Start API development server
- `dev:web` - Start web development server
- `test:coverage` - Run test coverage reporting

#### 4. Dependency Management
**Agent Prompt**: "Install the following dependencies if not present:"
**Core Dependencies:**
- `typescript` - TypeScript compiler and types
- `express` - Web framework for API
- `react` - Frontend framework
- `jest` - Testing framework for API
- `vitest` - Testing framework for web
- `eslint` - Code linting
- `prettier` - Code formatting

### 20% Milestone-Specific Execution

#### Infrastructure-Specific Tasks

**Agent Prompt**: "You are implementing a repository skeleton with CI/CD infrastructure. Follow these specific steps:"

1. **Initialize Monorepo Structure**
   ```bash
   # Create pnpm workspace configuration
   echo 'packages:\n  - "apps/*"\n  - "packages/*"' > pnpm-workspace.yaml
   
   # Initialize root package.json with workspace scripts
   pnpm init
   ```

2. **Scaffold API Application**
   ```bash
   mkdir -p apps/api/src
   cd apps/api
   # Create Express server with health endpoint
   # Use exact configuration from milestone specification
   ```

3. **Scaffold Web Application**
   ```bash
   mkdir -p apps/web/src
   cd apps/web
   # Create Vite React application
   # Configure health endpoint proxy to API
   ```

4. **Create Shared Package**
   ```bash
   mkdir -p packages/shared/src
   cd packages/shared
   # Create Result type and common utilities
   # Export from index.ts for workspace consumption
   ```

5. **Configure Quality Tools**
   ```bash
   # Setup ESLint configuration (.eslintrc.cjs)
   # Setup Prettier configuration (.prettierrc)
   # Configure Jest for API testing
   # Configure Vitest for web testing
   # Setup coverage reporting with 80% thresholds
   ```

6. **Setup CI/CD Pipeline**
   ```bash
   mkdir -p .github/workflows
   # Create ci.yml with exact configuration from specification
   # Include lint, test, build, coverage, and acceptance testing
   ```

7. **Docker Configuration**
   ```bash
   # Create Dockerfile for production builds
   # Create docker-compose.yml with API and Neo4j services
   # Configure health checks and service dependencies
   ```

8. **Acceptance Testing**
   ```bash
   mkdir -p scripts
   # Create m0-acceptance.sh with comprehensive validation
   # Include Docker health checks, not just direct API testing
   ```

## 📋 Task Execution Sequence

**Agent Prompt**: "Execute the following tasks in sequence. Each task should be completed on its own branch:"

**Task 01**: Initialise empty Git repo on GitHub
- Branch: `m0/init-repo`
- Owner: Lead
- **Agent Action**: Create repository, setup main branch, configure basic settings

**Task 02**: Add `pnpm-workspace.yaml`, run `pnpm init`
- Branch: `m0/workspace-file`
- Owner: BE
- **Agent Action**: Create workspace config, initialize package.json, verify pnpm install

**Task 03**: Scaffold **api** app (`express`, `tsup`)
- Branch: `m0/scaffold-api`
- Owner: BE
- **Agent Action**: Create Express server, configure tsup build, add health endpoint

**Task 04**: Scaffold **web** app (Vite React)
- Branch: `m0/scaffold-web`
- Owner: FE
- **Agent Action**: Create Vite React app, configure development server on port 5173

**Task 05**: Create **shared** package & export `Result` type
- Branch: `m0/shared-lib`
- Owner: BE
- **Agent Action**: Create shared package, implement Result type, configure exports

**Task 06**: Configure ESLint & Prettier root configs
- Branch: `m0/lint-config`
- Owner: FE
- **Agent Action**: Setup linting rules, configure Prettier, ensure pnpm lint passes

**Task 07**: Add sample unit tests (Jest, Vitest)
- Branch: `m0/sample-tests`
- Owner: BE & FE
- **Agent Action**: Create test suites for API and web, configure coverage reporting

**Task 08**: Author GitHub Actions `ci.yml`
- Branch: `m0/ci-pipeline`
- Owner: DevOps
- **Agent Action**: Create CI workflow, configure all validation steps

**Task 09**: Author `Dockerfile` & `docker-compose.yml`
- Branch: `m0/docker-stack`
- Owner: DevOps
- **Agent Action**: Create Docker configuration, setup health checks

**Task 10**: Add `scripts/m0-acceptance.sh` + README badge
- Branch: `m0/acceptance`
- Owner: DevOps
- **Agent Action**: Create acceptance script, update README with CI badge

**Task 11**: Ensure spec passes `spec-lint` & mark **Approved**
- Branch: `m0/spec-quality`
- Owner: PM
- **Agent Action**: Validate specification quality, update status

**Task 12**: Merge all PRs, tag `v0.0.1`, close milestone
- Branch: `m0/final-tag`
- Owner: Lead
- **Agent Action**: Merge branches, create release tag, verify CI

## ✅ Success Criteria Validation

**Agent Prompt**: "Validate each success criterion before marking milestone complete:"

- **Authoritative Test**: `bash scripts/m0-acceptance.sh` (must pass)
- Clean install and build process (≤ 60s)
- Code quality validation (lint + type-check)
- Test suite with coverage reporting (80% threshold)
- Docker compose stack health validation
- Specification validation with spec-lint
- Agent dry-run validation

**Validation Commands:**
- `pnpm install && pnpm lint && pnpm test && pnpm type-check`
- `docker compose up -d --wait && curl http://localhost:3000/health`
- `node scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.mdx`
- `pnpm run agent:dry-run`
- `bash scripts/m0-acceptance.sh`

## 🧪 Continuous Validation
Run these commands throughout implementation:

```bash
# After each major step
pnpm install && pnpm build

# Before committing
pnpm lint && pnpm type-check && pnpm test

# Docker validation
docker compose up -d --wait
curl -f http://localhost:3000/health
docker compose down

# Final validation
bash scripts/m0-acceptance.sh
```

## 🔄 Rollback Strategy
If execution fails:
1. **Identify failure point**: Check which task/acceptance criterion failed
2. **Isolate issue**: Run specific validation commands
3. **Fix and retry**: Address specific issue and re-validate
4. **Reset if needed**: `git checkout main && git branch -D implement-M0`

## 🤖 Agent-Specific Instructions

### For LLM Agents
- Use the prompts above as direct instructions
- Validate each step before proceeding to the next
- Follow exact file stubs and configurations provided in specification

### For Autonomous Agents
- Execute validation commands before each major step
- Use exit codes to determine success/failure
- Implement retry logic for Docker and network operations

### For Human-Agent Collaboration
- Human reviews milestone analysis and confidence score
- Agent executes standard 80% infrastructure pattern
- Human handles complex Docker and CI/CD configuration decisions

---
*Generated by auto-analyze-milestone.sh*
*Confidence Score: 93% | Checks: 14/15*
