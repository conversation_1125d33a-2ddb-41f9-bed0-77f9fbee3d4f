#!/usr/bin/env bash
set -euo pipefail

# Automated Milestone Analysis Script
# Performs comprehensive milestone analysis for agent execution readiness

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OUTPUT_DIR="$SCRIPT_DIR"
CONFIDENCE_THRESHOLD=95
VERBOSE=false
SCRIPTS_ONLY=false
CONFIG_FILE=""

# Helper functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warn() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

show_usage() {
    cat << EOF
Usage: $0 <milestone-file> [options]

Automated milestone analysis for agent execution readiness.

Arguments:
    milestone-file    Path to milestone MDX file

Options:
    --config FILE     Use custom analysis configuration
    --threshold NUM   Set confidence threshold (default: 95)
    --scripts-only    Generate only validation scripts
    --verbose         Enable verbose output
    --output-dir DIR  Set output directory (default: docs/scripts)
    --help           Show this help message

Examples:
    $0 docs/tech-specs/milestones/milestone-M0.2.mdx
    $0 milestone-M1.mdx --threshold 90 --verbose
    $0 milestone-M2.mdx --scripts-only --output-dir ./validation

EOF
}

parse_arguments() {
    if [[ $# -eq 0 ]]; then
        show_usage
        exit 1
    fi

    MILESTONE_FILE="$1"
    shift

    while [[ $# -gt 0 ]]; do
        case $1 in
            --config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            --threshold)
                CONFIDENCE_THRESHOLD="$2"
                shift 2
                ;;
            --scripts-only)
                SCRIPTS_ONLY=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --output-dir)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    # Validate milestone file exists
    if [[ ! -f "$MILESTONE_FILE" ]]; then
        log_error "Milestone file not found: $MILESTONE_FILE"
        exit 1
    fi

    # Extract milestone identifier from filename
    MILESTONE_ID=$(basename "$MILESTONE_FILE" .mdx | sed 's/milestone-//')
}

extract_milestone_info() {
    log_info "Extracting milestone information..."

    # Extract title and basic info from frontmatter
    MILESTONE_TITLE=$(grep -A 10 "^---" "$MILESTONE_FILE" | grep "title:" | cut -d':' -f2- | xargs)
    MILESTONE_VERSION=$(grep -A 10 "^---" "$MILESTONE_FILE" | grep "version:" | cut -d':' -f2- | xargs)
    MILESTONE_STATUS=$(grep -A 10 "^---" "$MILESTONE_FILE" | grep "status:" | cut -d':' -f2- | xargs)

    if [[ $VERBOSE == true ]]; then
        log_info "Title: $MILESTONE_TITLE"
        log_info "Version: $MILESTONE_VERSION"
        log_info "Status: $MILESTONE_STATUS"
    fi
}

run_core_analysis() {
    log_info "Running core milestone analysis..."

    # Generate the core analyzer if it doesn't exist
    if [[ ! -f "$SCRIPT_DIR/analyze-milestone-core.mjs" ]]; then
        generate_core_analyzer
    fi

    # Run analysis and capture results
    if command -v jq >/dev/null 2>&1; then
        ANALYSIS_RESULT=$(node "$SCRIPT_DIR/analyze-milestone-core.mjs" "$MILESTONE_FILE" --json)
        CONFIDENCE_SCORE=$(echo "$ANALYSIS_RESULT" | jq -r '.confidence_score')
        TOTAL_CHECKS=$(echo "$ANALYSIS_RESULT" | jq -r '.total_checks')
        PASSED_CHECKS=$(echo "$ANALYSIS_RESULT" | jq -r '.passed_checks')
        ERRORS=$(echo "$ANALYSIS_RESULT" | jq -r '.errors | length')
        WARNINGS=$(echo "$ANALYSIS_RESULT" | jq -r '.warnings | length')
    else
        # Fallback without jq
        node "$SCRIPT_DIR/analyze-milestone-core.mjs" "$MILESTONE_FILE" > /tmp/analysis_output.txt
        CONFIDENCE_SCORE=$(grep "Confidence:" /tmp/analysis_output.txt | cut -d' ' -f2 | tr -d '%')
        TOTAL_CHECKS=$(grep "Checks:" /tmp/analysis_output.txt | cut -d'/' -f2)
        PASSED_CHECKS=$(grep "Checks:" /tmp/analysis_output.txt | cut -d':' -f2 | cut -d'/' -f1 | xargs)
        ERRORS=0
        WARNINGS=0
    fi

    log_success "Analysis complete: $CONFIDENCE_SCORE% confidence ($PASSED_CHECKS/$TOTAL_CHECKS checks passed)"

    if [[ $ERRORS -gt 0 ]]; then
        log_warn "$ERRORS error(s) found"
    fi

    if [[ $WARNINGS -gt 0 ]]; then
        log_warn "$WARNINGS warning(s) found"
    fi
}

generate_validation_scripts() {
    log_info "Generating validation scripts..."

    # Generate milestone-specific validation script
    VALIDATION_SCRIPT="$OUTPUT_DIR/validate-${MILESTONE_ID}.mjs"

    # Extract milestone-specific content for intelligent generation
    MILESTONE_CONTENT=$(cat "$MILESTONE_FILE")

    cat > "$VALIDATION_SCRIPT" << EOF
#!/usr/bin/env node

/**
 * Auto-generated validation script for $MILESTONE_ID
 * Generated by auto-analyze-milestone.sh
 *
 * This script provides 80% standard validation + 20% milestone-specific checks
 */

import { readFileSync, existsSync } from 'fs';

class $(echo ${MILESTONE_ID} | sed 's/\./_/g')Validator {
  constructor() {
    this.checks = 0;
    this.passed = 0;
    this.errors = [];
    this.warnings = [];
  }

  // 80% STANDARD VALIDATIONS - Common to all milestones
  validateProjectStructure() {
    console.log('📁 Validating project structure...');

    const requiredDirs = [
      'code/packages',
      'code/apps',
      'docs/tech-specs'
    ];

    requiredDirs.forEach(dir => {
      this.checks++;
      if (existsSync(dir)) {
        console.log(\`✅ Directory exists: \${dir}\`);
        this.passed++;
      } else {
        console.log(\`❌ Missing directory: \${dir}\`);
        this.errors.push(\`Missing required directory: \${dir}\`);
      }
    });
  }

  validatePackageConfiguration() {
    console.log('📦 Validating package configuration...');

    this.checks++;
    if (existsSync('code/package.json')) {
      console.log('✅ Main package.json exists');
      this.passed++;

      const pkg = JSON.parse(readFileSync('code/package.json', 'utf8'));

      // Check for required scripts based on milestone
      const requiredScripts = this.getRequiredScripts();
      requiredScripts.forEach(script => {
        this.checks++;
        if (pkg.scripts && pkg.scripts[script]) {
          console.log(\`✅ Script exists: \${script}\`);
          this.passed++;
        } else {
          console.log(\`⚠️  Missing script: \${script}\`);
          this.warnings.push(\`Consider adding script: \${script}\`);
        }
      });
    } else {
      console.log('❌ Main package.json missing');
      this.errors.push('Main package.json file not found');
    }
  }

  validateDependencies() {
    console.log('🔧 Validating dependencies...');

    const requiredDeps = this.getRequiredDependencies();

    if (existsSync('code/package.json')) {
      const pkg = JSON.parse(readFileSync('code/package.json', 'utf8'));
      const allDeps = { ...pkg.dependencies, ...pkg.devDependencies };

      requiredDeps.forEach(dep => {
        this.checks++;
        if (allDeps[dep]) {
          console.log(\`✅ Dependency available: \${dep}\`);
          this.passed++;
        } else {
          console.log(\`⚠️  Missing dependency: \${dep}\`);
          this.warnings.push(\`Consider installing: \${dep}\`);
        }
      });
    }
  }

  // 20% MILESTONE-SPECIFIC VALIDATIONS - Generated from milestone content
  validateMilestoneSpecificRequirements() {
    console.log('🎯 Validating milestone-specific requirements...');

$(generate_milestone_specific_validations "$MILESTONE_CONTENT")
  }

  getRequiredScripts() {
$(generate_required_scripts "$MILESTONE_CONTENT")
  }

  getRequiredDependencies() {
$(generate_required_dependencies "$MILESTONE_CONTENT")
  }

  generateReport() {
    console.log('\\n📊 Validation Summary');
    console.log('===================');
    console.log(\`Total checks: \${this.checks}\`);
    console.log(\`Passed: \${this.passed}\`);
    console.log(\`Errors: \${this.errors.length}\`);
    console.log(\`Warnings: \${this.warnings.length}\`);

    const successRate = Math.round((this.passed / this.checks) * 100);
    console.log(\`Success rate: \${successRate}%\`);

    if (this.errors.length > 0) {
      console.log('\\n🔴 Errors:');
      this.errors.forEach(error => console.log(\`   • \${error}\`));
    }

    if (this.warnings.length > 0) {
      console.log('\\n🟡 Warnings:');
      this.warnings.forEach(warning => console.log(\`   • \${warning}\`));
    }

    return successRate >= 90;
  }

  run() {
    console.log('🔍 Validating $MILESTONE_ID milestone...');
    console.log('=====================================\\n');

    this.validateProjectStructure();
    this.validatePackageConfiguration();
    this.validateDependencies();
    this.validateMilestoneSpecificRequirements();

    const success = this.generateReport();
    process.exit(success ? 0 : 1);
  }
}

const validator = new $(echo ${MILESTONE_ID} | sed 's/\./_/g')Validator();
validator.run();
EOF

    chmod +x "$VALIDATION_SCRIPT"
    log_success "Generated: $VALIDATION_SCRIPT"
}

generate_acceptance_tests() {
    log_info "Generating acceptance test suite..."

    # Ensure acceptance directory exists
    mkdir -p "$OUTPUT_DIR/acceptance"

    ACCEPTANCE_SCRIPT="$OUTPUT_DIR/acceptance/${MILESTONE_ID}-acceptance.sh"

    cat > "$ACCEPTANCE_SCRIPT" << EOF
#!/usr/bin/env bash
set -euo pipefail

echo "🔧 Running $MILESTONE_ID Acceptance Tests..."

# Auto-generated acceptance test suite
# Customize based on milestone requirements

# Test 1: Validation script passes
echo "1️⃣ Testing validation script..."
if node "$OUTPUT_DIR/validate-${MILESTONE_ID}.mjs"; then
    echo "✅ Validation passed"
else
    echo "❌ Validation failed"
    exit 1
fi

# Add more tests based on milestone deliverables

echo "🎉 All $MILESTONE_ID acceptance tests passed!"
EOF

    chmod +x "$ACCEPTANCE_SCRIPT"
    log_success "Generated: $ACCEPTANCE_SCRIPT"
}

generate_execution_guide() {
    log_info "Generating execution guide..."

    GUIDE_FILE="$OUTPUT_DIR/agent-execution-guide-${MILESTONE_ID}.md"

    # Extract milestone-specific content for intelligent generation
    MILESTONE_CONTENT=$(cat "$MILESTONE_FILE")

    cat > "$GUIDE_FILE" << EOF
# $MILESTONE_ID Agent Execution Guide

## 🎯 Execution Overview
**Milestone**: $MILESTONE_TITLE
**Confidence**: $CONFIDENCE_SCORE%
**Status**: $([ $CONFIDENCE_SCORE -ge $CONFIDENCE_THRESHOLD ] && echo "READY FOR EXECUTION" || echo "NEEDS ATTENTION")

## 🚀 Agent Execution Prompts

### 80% Standard Execution Pattern
Use these prompts for any milestone execution:

#### 1. Pre-Execution Setup
\`\`\`bash
# Validate milestone readiness
node docs/scripts/validate-${MILESTONE_ID}.mjs

# Ensure dependencies are installed
cd code && pnpm install && cd ..

# Create feature branch
git checkout -b implement-${MILESTONE_ID}
\`\`\`

#### 2. Standard Project Structure Validation
**Agent Prompt**: "Verify the following directories exist and create them if missing:"
$(generate_standard_structure_prompts)

#### 3. Package Configuration Setup
**Agent Prompt**: "Ensure the following scripts are available in package.json:"
$(generate_package_script_prompts "$MILESTONE_CONTENT")

#### 4. Dependency Management
**Agent Prompt**: "Install the following dependencies if not present:"
$(generate_dependency_prompts "$MILESTONE_CONTENT")

### 20% Milestone-Specific Execution
$(generate_milestone_specific_prompts "$MILESTONE_CONTENT")

## 📋 Task Execution Sequence
$(extract_task_breakdown "$MILESTONE_CONTENT")

## ✅ Success Criteria Validation
$(extract_success_criteria "$MILESTONE_CONTENT")

## 🧪 Continuous Validation
Run these commands throughout implementation:

\`\`\`bash
# After each major step
node docs/scripts/validate-${MILESTONE_ID}.mjs

# Before committing
bash docs/scripts/acceptance/${MILESTONE_ID}-acceptance.sh

# Final validation
pnpm run build && pnpm run test && pnpm run lint
\`\`\`

## 🔄 Rollback Strategy
If execution fails:
1. **Identify failure point**: Check validation output
2. **Isolate issue**: Run specific validation checks
3. **Fix and retry**: Address issue and re-validate
4. **Reset if needed**: \`git checkout main && git branch -D implement-${MILESTONE_ID}\`

## 🤖 Agent-Specific Instructions

### For LLM Agents
- Use the prompts above as direct instructions
- Validate each step before proceeding
- Ask for clarification if any step is unclear

### For Autonomous Agents
- Execute validation scripts before each major step
- Use exit codes to determine success/failure
- Implement retry logic for transient failures

### For Human-Agent Collaboration
- Human reviews milestone analysis
- Agent executes standard 80% pattern
- Human handles complex 20% decisions

---
*Generated by auto-analyze-milestone.sh on $(date)*
*Confidence Score: $CONFIDENCE_SCORE% | Checks: $PASSED_CHECKS/$TOTAL_CHECKS*
EOF

    log_success "Generated: $GUIDE_FILE"
}

generate_analysis_report() {
    log_info "Generating analysis report..."

    REPORT_FILE="$OUTPUT_DIR/${MILESTONE_ID}-analysis-summary.md"

    cat > "$REPORT_FILE" << EOF
# $MILESTONE_ID Analysis Summary

## Executive Summary
**Confidence Score: $CONFIDENCE_SCORE%**
**Status: $([ $CONFIDENCE_SCORE -ge $CONFIDENCE_THRESHOLD ] && echo "READY FOR EXECUTION" || echo "NEEDS ATTENTION")**

## Validation Results
- Total Checks: $TOTAL_CHECKS
- Passed: $PASSED_CHECKS
- Errors: $ERRORS
- Warnings: $WARNINGS

## Generated Artifacts
- Validation Script: \`validate-${MILESTONE_ID}.mjs\`
- Acceptance Tests: \`acceptance/${MILESTONE_ID}-acceptance.sh\`
- Execution Guide: \`agent-execution-guide-${MILESTONE_ID}.md\`

## Recommendation
$([ $CONFIDENCE_SCORE -ge $CONFIDENCE_THRESHOLD ] && echo "✅ PROCEED WITH AGENT EXECUTION" || echo "⚠️ RESOLVE ISSUES BEFORE EXECUTION")

---
*Analysis completed on $(date)*
*Generated by auto-analyze-milestone.sh*
EOF

    log_success "Generated: $REPORT_FILE"
}

# Helper functions for intelligent content generation
generate_milestone_specific_validations() {
    local content="$1"

    # Extract deliverables and generate specific validations
    if echo "$content" | grep -q "knowledge.*graph\|JSON-LD\|YAML.*graph"; then
        cat << 'EOF'
    // Knowledge Graph specific validations
    this.checks++;
    if (existsSync('kg-schema.yml')) {
      console.log('✅ Knowledge graph schema exists');
      this.passed++;
    } else {
      console.log('⚠️  Missing kg-schema.yml');
      this.warnings.push('Knowledge graph schema file not found');
    }

    this.checks++;
    if (existsSync('code/packages/spec-parser-lib')) {
      console.log('✅ Spec parser library directory exists');
      this.passed++;
    } else {
      console.log('❌ Missing spec-parser-lib package');
      this.errors.push('Spec parser library package not found');
    }

    this.checks++;
    if (existsSync('code/packages/kg-cli')) {
      console.log('✅ Knowledge graph CLI directory exists');
      this.passed++;
    } else {
      console.log('❌ Missing kg-cli package');
      this.errors.push('Knowledge graph CLI package not found');
    }
EOF
    elif echo "$content" | grep -q "api\|REST\|GraphQL"; then
        cat << 'EOF'
    // API specific validations
    this.checks++;
    if (existsSync('code/apps/api')) {
      console.log('✅ API application directory exists');
      this.passed++;
    } else {
      console.log('❌ Missing API application directory');
      this.errors.push('API application directory not found');
    }

    this.checks++;
    const apiFiles = ['routes', 'controllers', 'middleware'].some(dir =>
      existsSync(`code/apps/api/${dir}`)
    );
    if (apiFiles) {
      console.log('✅ API structure directories exist');
      this.passed++;
    } else {
      console.log('⚠️  API structure incomplete');
      this.warnings.push('Consider creating routes, controllers, middleware directories');
    }
EOF
    elif echo "$content" | grep -q "docs\|documentation\|docusaurus"; then
        cat << 'EOF'
    // Documentation specific validations
    this.checks++;
    if (existsSync('code/apps/docs-site')) {
      console.log('✅ Documentation site directory exists');
      this.passed++;
    } else {
      console.log('❌ Missing documentation site directory');
      this.errors.push('Documentation site directory not found');
    }

    this.checks++;
    if (existsSync('code/apps/docs-site/docusaurus.config.js')) {
      console.log('✅ Docusaurus configuration exists');
      this.passed++;
    } else {
      console.log('⚠️  Missing Docusaurus configuration');
      this.warnings.push('Docusaurus configuration file not found');
    }
EOF
    else
        cat << 'EOF'
    // Generic milestone validations
    this.checks++;
    console.log('ℹ️  No specific milestone type detected - using generic validations');
    this.passed++;
EOF
    fi
}

generate_required_scripts() {
    local content="$1"

    echo "    return ["

    # Always include basic scripts
    echo "      'build',"
    echo "      'test',"
    echo "      'lint',"

    # Add milestone-specific scripts
    if echo "$content" | grep -q "build-kg\|knowledge.*graph"; then
        echo "      'build-kg',"
    fi

    if echo "$content" | grep -q "docs.*build\|docusaurus"; then
        echo "      'docs:build',"
        echo "      'docs:start',"
    fi

    if echo "$content" | grep -q "dev.*api\|api.*dev"; then
        echo "      'dev:api',"
    fi

    if echo "$content" | grep -q "dev.*web\|web.*dev"; then
        echo "      'dev:web',"
    fi

    echo "    ];"
}

generate_required_dependencies() {
    local content="$1"

    echo "    return ["

    # Always include basic dependencies
    echo "      'typescript',"

    # Add milestone-specific dependencies
    if echo "$content" | grep -q "gray-matter"; then
        echo "      'gray-matter',"
    fi

    if echo "$content" | grep -q "yaml"; then
        echo "      'yaml',"
    fi

    if echo "$content" | grep -q "uuid"; then
        echo "      'uuid',"
    fi

    if echo "$content" | grep -q "jest"; then
        echo "      'jest',"
    fi

    if echo "$content" | grep -q "docusaurus"; then
        echo "      '@docusaurus/core',"
        echo "      '@docusaurus/preset-classic',"
    fi

    if echo "$content" | grep -q "express\|fastify\|api"; then
        echo "      'express',"
    fi

    echo "    ];"
}

# Additional helper functions for execution guide generation
generate_standard_structure_prompts() {
    cat << 'EOF'
- `code/packages/` - For reusable packages and libraries
- `code/apps/` - For applications (api, web, docs-site)
- `docs/tech-specs/` - For technical specifications
- `.github/workflows/` - For CI/CD workflows
EOF
}

generate_package_script_prompts() {
    local content="$1"

    echo "- \`build\` - Build all packages and applications"
    echo "- \`test\` - Run test suites"
    echo "- \`lint\` - Run linting and code quality checks"

    if echo "$content" | grep -q "build-kg\|knowledge.*graph"; then
        echo "- \`build-kg\` - Build knowledge graph from specifications"
    fi

    if echo "$content" | grep -q "docs.*build\|docusaurus"; then
        echo "- \`docs:build\` - Build documentation site"
        echo "- \`docs:start\` - Start documentation development server"
    fi
}

generate_dependency_prompts() {
    local content="$1"

    echo "**Core Dependencies:**"
    echo "- \`typescript\` - TypeScript compiler and types"

    if echo "$content" | grep -q "gray-matter"; then
        echo "- \`gray-matter\` - Front-matter parsing for MDX files"
    fi

    if echo "$content" | grep -q "yaml"; then
        echo "- \`yaml\` - YAML parsing and serialization"
    fi

    if echo "$content" | grep -q "uuid"; then
        echo "- \`uuid\` - UUID generation for unique identifiers"
    fi

    if echo "$content" | grep -q "jest"; then
        echo "- \`jest\` - Testing framework"
    fi
}

generate_milestone_specific_prompts() {
    local content="$1"

    if echo "$content" | grep -q "knowledge.*graph\|JSON-LD\|YAML.*graph"; then
        cat << 'EOF'

#### Knowledge Graph Specific Tasks

**Agent Prompt**: "You are implementing a knowledge graph system. Follow these specific steps:"

1. **Create Spec Parser Library**
   ```bash
   mkdir -p code/packages/spec-parser-lib/src
   cd code/packages/spec-parser-lib
   # Initialize package.json with dependencies: gray-matter, yaml, uuid
   ```

2. **Implement MDX Parsing**
   - Create `src/parse-specs.ts` that can extract frontmatter from MDX files
   - Use gray-matter library for frontmatter parsing
   - Extract headings and content structure

3. **Create Knowledge Graph CLI**
   ```bash
   mkdir -p code/packages/kg-cli/src
   cd code/packages/kg-cli
   # Create CLI tool that reads specs and outputs graph files
   ```

4. **Implement Graph Generation**
   - Create `src/build-kg.ts` that scans `docs/tech-specs/**/*.mdx`
   - Generate both `kg.jsonld` and `kg.yaml` files
   - Support `--dry-run` mode for validation

5. **Create Schema Definition**
   - Create `kg-schema.yml` in repository root
   - Define entities: milestone, component, relationship types
   - Include confidence scoring and metadata
EOF
    elif echo "$content" | grep -q "api\|REST\|GraphQL"; then
        cat << 'EOF'

#### API Development Specific Tasks

**Agent Prompt**: "You are implementing an API system. Follow these specific steps:"

1. **Setup API Application Structure**
   ```bash
   mkdir -p code/apps/api/{routes,controllers,middleware,models}
   cd code/apps/api
   # Initialize with Express.js or preferred framework
   ```

2. **Implement Core API Features**
   - Create route definitions in `routes/`
   - Implement business logic in `controllers/`
   - Add middleware for authentication, validation, logging
   - Define data models in `models/`

3. **Add API Documentation**
   - Create OpenAPI/Swagger specification
   - Document all endpoints with examples
   - Include authentication and error handling docs
EOF
    elif echo "$content" | grep -q "docs\|documentation\|docusaurus"; then
        cat << 'EOF'

#### Documentation Site Specific Tasks

**Agent Prompt**: "You are implementing a documentation site. Follow these specific steps:"

1. **Setup Docusaurus Site**
   ```bash
   cd code/apps
   pnpm create docusaurus@latest docs-site classic --typescript
   cd docs-site
   ```

2. **Configure Documentation Source**
   - Edit `docusaurus.config.js` to point to `../../docs/tech-specs`
   - Configure sidebar navigation in `sidebars.js`
   - Setup search functionality

3. **Customize Site**
   - Add custom components for Callouts and MDX elements
   - Configure theme and styling
   - Setup deployment to GitHub Pages
EOF
    else
        echo ""
        echo "**Agent Prompt**: \"This milestone doesn't match standard patterns. Carefully review the deliverables and task breakdown to understand the specific requirements.\""
    fi
}

extract_task_breakdown() {
    local content="$1"

    echo ""
    echo "**Agent Prompt**: \"Execute the following tasks in sequence. Each task should be completed on its own branch:\""
    echo ""

    # Extract task breakdown table
    echo "$content" | sed -n '/## 🔨 Task Breakdown/,/^##/p' | grep '|.*|.*|.*|' | grep -v '|---|' | tail -n +2 | while IFS='|' read -r num branch task owner rest; do
        if [[ -n "$num" && "$num" != *"#"* ]]; then
            num=$(echo "$num" | xargs)
            branch=$(echo "$branch" | xargs | sed 's/`//g')
            task=$(echo "$task" | xargs)
            owner=$(echo "$owner" | xargs)

            if [[ -n "$num" && -n "$branch" && -n "$task" ]]; then
                echo "**Task $num**: $task"
                echo "- Branch: \`$branch\`"
                echo "- Owner: $owner"
                echo "- **Agent Action**: Create branch, implement task, test, and create PR"
                echo ""
            fi
        fi
    done
}

extract_success_criteria() {
    local content="$1"

    echo ""
    echo "**Agent Prompt**: \"Validate each success criterion before marking milestone complete:\""
    echo ""

    # Extract success criteria
    echo "$content" | sed -n '/## ✅ Success Criteria/,/^##/p' | grep -E '- \[ \].*SC-[0-9]+' | while read -r line; do
        criterion=$(echo "$line" | sed 's/- \[ \] \*\*SC-[0-9]*\*\* //')
        echo "- $criterion"
    done

    echo ""
    echo "**Validation Commands:**"
    echo "$content" | sed -n '/## ✅ Success Criteria/,/^##/p' | grep -A 3 '```bash' | grep -v '```' | grep -v '^--$' | while read -r cmd; do
        if [[ -n "$cmd" && "$cmd" != *"#"* ]]; then
            echo "\`$cmd\`"
        fi
    done
}

generate_core_analyzer() {
    log_info "Generating core analyzer..."

    cat > "$SCRIPT_DIR/analyze-milestone-core.mjs" << 'EOF'
#!/usr/bin/env node

/**
 * Core Milestone Analyzer
 * Performs comprehensive milestone analysis
 */

import { readFileSync, existsSync } from 'fs';

class MilestoneAnalyzer {
  constructor() {
    this.checks = 0;
    this.passed = 0;
    this.errors = [];
    this.warnings = [];
  }

  analyze(milestoneFile) {
    if (!existsSync(milestoneFile)) {
      this.errors.push(`Milestone file not found: ${milestoneFile}`);
      return this.getResults();
    }

    const content = readFileSync(milestoneFile, 'utf8');

    this.validateFrontmatter(content);
    this.validateSections(content);
    this.validateTaskBreakdown(content);
    this.validateSuccessCriteria(content);

    return this.getResults();
  }

  validateFrontmatter(content) {
    const requiredFields = ['title', 'description', 'version', 'status'];
    const frontmatterMatch = content.match(/^---\s*\n([\s\S]*?)\n---/);

    if (!frontmatterMatch) {
      this.errors.push('Missing frontmatter');
      return;
    }

    requiredFields.forEach(field => {
      this.checks++;
      if (frontmatterMatch[1].includes(`${field}:`)) {
        this.passed++;
      } else {
        this.errors.push(`Missing frontmatter field: ${field}`);
      }
    });
  }

  validateSections(content) {
    const requiredSections = [
      'Definition of Done',
      'Deliverables',
      'Task Breakdown',
      'Success Criteria'
    ];

    requiredSections.forEach(section => {
      this.checks++;
      if (content.includes(section)) {
        this.passed++;
      } else {
        this.errors.push(`Missing section: ${section}`);
      }
    });
  }

  validateTaskBreakdown(content) {
    this.checks++;
    const taskMatches = content.match(/\| \d+ \|.*\|.*\|.*\|/g);
    if (taskMatches && taskMatches.length >= 3) {
      this.passed++;
    } else {
      this.errors.push('Insufficient task breakdown');
    }
  }

  validateSuccessCriteria(content) {
    this.checks++;
    const criteriaMatches = content.match(/- \[ \] \*\*SC-\d+\*\*/g);
    if (criteriaMatches && criteriaMatches.length >= 3) {
      this.passed++;
    } else {
      this.errors.push('Insufficient success criteria');
    }
  }

  getResults() {
    const confidence_score = Math.round((this.passed / this.checks) * 100);

    return {
      confidence_score,
      total_checks: this.checks,
      passed_checks: this.passed,
      errors: this.errors,
      warnings: this.warnings
    };
  }
}

// CLI interface
const milestoneFile = process.argv[2];
const outputJson = process.argv.includes('--json');

if (!milestoneFile) {
  console.error('Usage: node analyze-milestone-core.mjs <milestone-file> [--json]');
  process.exit(1);
}

const analyzer = new MilestoneAnalyzer();
const results = analyzer.analyze(milestoneFile);

if (outputJson) {
  console.log(JSON.stringify(results, null, 2));
} else {
  console.log(`Confidence: ${results.confidence_score}%`);
  console.log(`Checks: ${results.passed_checks}/${results.total_checks}`);
  if (results.errors.length > 0) {
    console.log('Errors:', results.errors);
  }
}

process.exit(results.confidence_score >= 95 ? 0 : 1);
EOF

    chmod +x "$SCRIPT_DIR/analyze-milestone-core.mjs"
}

main() {
    echo "🚀 Automated Milestone Analysis"
    echo "================================"

    parse_arguments "$@"
    extract_milestone_info
    run_core_analysis

    if [[ $SCRIPTS_ONLY == false ]]; then
        generate_analysis_report
        generate_execution_guide
    fi

    generate_validation_scripts
    generate_acceptance_tests

    echo ""
    echo "📊 Analysis Summary"
    echo "==================="
    log_info "Milestone: $MILESTONE_TITLE"
    log_info "Confidence Score: $CONFIDENCE_SCORE%"
    log_info "Checks Passed: $PASSED_CHECKS/$TOTAL_CHECKS"

    if [[ $CONFIDENCE_SCORE -ge $CONFIDENCE_THRESHOLD ]]; then
        log_success "✅ MILESTONE READY FOR AGENT EXECUTION"
        echo ""
        echo "📁 Generated Files:"
        echo "   • Validation: validate-${MILESTONE_ID}.mjs"
        echo "   • Acceptance: acceptance/${MILESTONE_ID}-acceptance.sh"
        if [[ $SCRIPTS_ONLY == false ]]; then
            echo "   • Guide: agent-execution-guide-${MILESTONE_ID}.md"
            echo "   • Report: ${MILESTONE_ID}-analysis-summary.md"
        fi
        exit 0
    else
        log_warn "⚠️ MILESTONE NEEDS ATTENTION (${CONFIDENCE_SCORE}% < ${CONFIDENCE_THRESHOLD}%)"
        exit 1
    fi
}

main "$@"
