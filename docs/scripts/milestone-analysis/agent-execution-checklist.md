# M0.1 Agent Execution Checklist

## Pre-Execution Validation ✅

Run this checklist before beginning implementation:

```bash
# 1. Final validation (must pass with 95%+ confidence)
node docs/scripts/validate-m0.1-final.mjs
# Expected: Exit code 0, "READY FOR AGENT EXECUTION"

# 2. Ensure dependencies are available
cd code && pnpm install && cd ..

# 3. Verify workspace structure
ls -la code/packages code/apps docs/tech-specs
```

**✅ VALIDATION PASSED - PROCEED WITH EXECUTION**

## Task Execution Sequence

### Phase 1: Package Scaffolding

#### Task 01: Scaffold spec-parser-lib
- **Branch**: `m0.1/parser-lib`
- **Deliverable**: `code/packages/spec-parser-lib/` with TypeScript setup
- **Validation**: Directory exists, package.json created

#### Task 02: Implement MDX front-matter + heading extraction  
- **Branch**: `m0.1/parse-frontmatter`
- **Deliverable**: `parse-specs.ts` with gray-matter integration
- **Validation**: Can parse MDX files, extract frontmatter

#### Task 03: Commit kg-schema.yml
- **Branch**: `m0.1/kg-schema`
- **Deliverable**: `kg-schema.yml` (entities, relations)
- **Validation**: Schema file exists at repo root

### Phase 2: Core Implementation

#### Task 04: CLI build-kg.ts
- **Branch**: `m0.1/kg-cli`
- **Deliverable**: `code/packages/kg-cli/` with CLI implementation
- **Validation**: CLI reads specs, writes graphs

#### Task 05: Jest tests for parser & CLI
- **Branch**: `m0.1/tests`
- **Deliverable**: Test suites for both packages
- **Validation**: Tests pass, coverage adequate

### Phase 3: Integration & CI

#### Task 06: Add .github/workflows/graph.yml
- **Branch**: `m0.1/ci-graph`
- **Deliverable**: GitHub Actions workflow
- **Validation**: Workflow runs dry-run successfully

#### Task 07: .vscode/extensions.json + Obsidian README
- **Branch**: `m0.1/editor-support`
- **Deliverable**: Editor configuration files
- **Validation**: Files exist, recommendations correct

### Phase 4: Quality & Finalization

#### Task 08: Run spec-lint; mark spec Approved
- **Branch**: `m0.1/spec-quality`
- **Deliverable**: Spec passes all quality checks
- **Validation**: Spec lint passes, status updated

#### Task 09: Merge & tag kg-bootstrap-v0.1.0
- **Branch**: `m0.1/final-tag`
- **Deliverable**: Tagged release
- **Validation**: All tests pass, tag created

## Success Criteria Validation

After implementation, verify each success criterion:

### SC-1: Dry-run test
```bash
pnpm run build-kg -- --dry-run docs/tech-specs
# Expected: Exit code 0, no files written
```

### SC-2: Full build test
```bash
pnpm run build-kg
ls kg.jsonld kg.yaml  # Both files should exist
```

### SC-3: CI test
```bash
# Push to branch, verify GitHub Actions pass
```

### SC-4: Graph content validation
```bash
# Check for required nodes and edges
grep -q "milestone" kg.yaml
grep -q "component" kg.yaml
grep -q "implements" kg.yaml
```

### SC-5: Spec lint validation
```bash
node scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx
# Expected: Exit code 0
```

### SC-6: Agent dry-run validation
```bash
pnpm run agent:dry-run --spec docs/tech-specs/milestones/milestone-M0.1.mdx
# Expected: Exit code 0
```

## Final Acceptance Test

```bash
# Run comprehensive acceptance test
bash docs/scripts/acceptance/m0.1-acceptance.sh
# Expected: All tests pass
```

## Quality Gates

Before marking milestone complete:

- [ ] All 9 tasks completed successfully
- [ ] All 6 success criteria pass
- [ ] Acceptance test suite passes
- [ ] CI pipeline is green
- [ ] Graph files generated correctly
- [ ] Documentation is complete

## Rollback Plan

If execution fails:

1. **Identify failure point**: Check which task/success criterion failed
2. **Isolate issue**: Use validation scripts to pinpoint problem
3. **Fix and retry**: Address specific issue and re-run from that point
4. **Full validation**: Re-run complete acceptance test

## Agent Confidence Indicators

### 🟢 High Confidence (Proceed)
- All validation scripts pass
- Clear error messages when issues occur
- Deterministic behavior in tests
- Successful dry-run operations

### 🟡 Medium Confidence (Proceed with caution)
- Minor warnings in validation
- Some tests pass with warnings
- Non-critical functionality issues

### 🔴 Low Confidence (Stop and review)
- Validation scripts fail
- Critical functionality broken
- Unpredictable behavior
- Major test failures

## Support Resources

### Validation Scripts
- `docs/scripts/validate-m0.1-final.mjs` - Comprehensive validation
- `docs/scripts/validate-task-breakdown.mjs` - Task analysis
- `docs/scripts/spec-lint.mjs` - Specification validation

### Documentation
- `docs/scripts/agent-execution-guide-m0.1.md` - Detailed implementation guide
- `docs/scripts/m0.1-analysis-summary.md` - Analysis summary
- `docs/tech-specs/milestones/milestone-M0.1.mdx` - Source specification

### Troubleshooting
- Check dependency installation: `cd code && pnpm install`
- Verify workspace configuration: `cat code/pnpm-workspace.yaml`
- Validate file permissions: `ls -la docs/scripts/`

---

**🎯 EXECUTION READY - CONFIDENCE LEVEL: 95%+**

This milestone has been thoroughly analyzed and validated. All systems are ready for agent execution.
