# Milestone Analysis Configuration
# Customize analysis behavior for different milestone types

# Analysis strictness level
strictness_level: "high"  # high, medium, low

# Confidence scoring with 70/30 principle
confidence:
  threshold: 95
  principle: "70/30"  # 70% standard patterns, 30% context-specific
  weights:
    # 70% Standard Validation Patterns
    standard_patterns:
      frontmatter: 15
      sections: 20
      task_breakdown: 20
      success_criteria: 15
      total_standard: 70

    # 30% Context-Specific Validation
    context_specific:
      domain_expertise: 15      # Deep domain knowledge validation
      project_adaptation: 10    # Project-specific patterns and learning
      execution_feedback: 5     # Learning from previous milestone executions
      total_context: 30

# Required frontmatter fields
required_frontmatter:
  - title
  - description
  - created
  - version
  - status
  - tags

# Optional frontmatter fields (warnings only)
optional_frontmatter:
  - updated
  - authors

# Required sections (exact matches)
required_sections:
  - "## 🧳 Toolchain Versions"
  - "## 🎯 Definition of Done"
  - "## 📦 Deliverables"
  - "## 🗂 Directory Layout"
  - "## 🧠 Key Decisions"
  - "## ✅ Success Criteria"
  - "## 🔨 Task Breakdown"
  - "## 🤖 CI Pipeline"
  - "## 🧪 Acceptance Tests"

# Minimum requirements
minimums:
  tasks: 5
  success_criteria: 3
  deliverables: 3

# Task validation
task_validation:
  require_branch_names: true
  require_owners: true
  branch_pattern: "^m\\d+\\.\\d+/[a-z-]+$"
  valid_owners: ["BE", "FE", "PM", "DevOps", "Lead"]
  action_verbs: ["scaffold", "implement", "create", "add", "commit", "run", "merge", "build", "test", "deploy"]

# Success criteria validation
success_criteria:
  pattern: "- \\[ \\] \\*\\*SC-\\d+\\*\\*"
  require_commands: true
  require_expected_results: true

# Deliverables validation
deliverables:
  require_paths: true
  require_descriptions: true
  check_coverage: true

# Output configuration
output:
  generate_validation_scripts: true
  generate_acceptance_tests: true
  generate_execution_guides: true
  generate_analysis_reports: true
  include_templates: true
  verbose_logging: false

# Template customization
templates:
  validation_script: "default"
  acceptance_test: "default"
  execution_guide: "default"
  analysis_report: "detailed"

# Agent-specific settings
agent_settings:
  execution_confidence_threshold: 95
  warning_tolerance: 5
  error_tolerance: 0
  require_deterministic_tests: true
  require_rollback_plan: false

# Enhanced validation settings
enhanced_validation:
  # Content quality analysis
  toolchain_compatibility:
    node_js_range: [18, 22]  # Supported Node.js major versions
    typescript_range: [4, 6]  # Supported TypeScript major versions
    check_package_compatibility: true

  # Cross-reference validation
  deliverable_task_alignment:
    minimum_coverage: 0.7  # 70% of deliverables should be mentioned in tasks
    check_file_extensions: [".ts", ".js", ".yml", ".yaml", ".json", ".md"]

  branch_naming:
    enforce_pattern: true
    pattern_compliance: 0.8  # 80% of branches should follow pattern
    check_duplicates: true

  # Domain-specific knowledge validation
  domain_validation:
    knowledge_graph:
      require_schema_definition: true
      require_parsing_strategy: true
      require_output_formats: true
      valid_formats: ["JSON-LD", "YAML", "RDF"]

    api_development:
      require_specification: true
      require_testing_strategy: true
      valid_specs: ["OpenAPI", "swagger", "GraphQL"]

    ci_cd:
      require_workflow_definition: true
      require_deployment_strategy: true
      check_artifact_handling: true

# Integration settings
integration:
  ci_cd: true
  github_actions: true
  quality_gates: true
  automated_reporting: true
