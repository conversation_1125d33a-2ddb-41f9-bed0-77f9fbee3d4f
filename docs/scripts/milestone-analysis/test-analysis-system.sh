#!/usr/bin/env bash
set -euo pipefail

# Test Suite for Milestone Analysis System
# Tests the 70/30 principle implementation and core functionality

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_OUTPUT_DIR="$SCRIPT_DIR/test-output"
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Helper functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warn() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_test() { echo -e "${CYAN}🧪 $1${NC}"; }

start_test() {
    local test_name="$1"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_test "Test $TOTAL_TESTS: $test_name"
}

pass_test() {
    PASSED_TESTS=$((PASSED_TESTS + 1))
    log_success "PASSED"
    echo ""
}

fail_test() {
    local reason="$1"
    FAILED_TESTS=$((FAILED_TESTS + 1))
    log_error "FAILED: $reason"
    echo ""
}

setup_test_environment() {
    log_info "Setting up test environment..."

    # Create test output directory
    mkdir -p "$TEST_OUTPUT_DIR"

    # Clean previous test artifacts
    rm -f "$SCRIPT_DIR"/validate-*.mjs
    rm -f "$SCRIPT_DIR"/*-analysis-summary.md
    rm -f "$SCRIPT_DIR"/agent-execution-guide-*.md
    rm -rf "$SCRIPT_DIR/acceptance"

    log_success "Test environment ready"
    echo ""
}

cleanup_test_environment() {
    log_info "Cleaning up test artifacts..."

    # Remove test-generated files
    rm -f "$SCRIPT_DIR"/validate-*.mjs
    rm -f "$SCRIPT_DIR"/*-analysis-summary.md
    rm -f "$SCRIPT_DIR"/agent-execution-guide-*.md
    rm -rf "$SCRIPT_DIR/acceptance"
    rm -rf "$TEST_OUTPUT_DIR"

    log_success "Cleanup complete"
}

# Test 1: Core analyzer functionality
test_core_analyzer() {
    start_test "Core analyzer with M0.1 milestone"

    if node "$SCRIPT_DIR/analyze-milestone-core.mjs" "docs/tech-specs/milestones/milestone-M0.1.mdx" --json > "$TEST_OUTPUT_DIR/core-analysis.json"; then
        # Check if JSON output is valid (fallback if jq not available)
        if command -v jq >/dev/null 2>&1; then
            if jq . "$TEST_OUTPUT_DIR/core-analysis.json" >/dev/null 2>&1; then
                local confidence=$(jq -r '.confidence_score' "$TEST_OUTPUT_DIR/core-analysis.json")
                local total_checks=$(jq -r '.total_checks' "$TEST_OUTPUT_DIR/core-analysis.json")
                local passed_checks=$(jq -r '.passed_checks' "$TEST_OUTPUT_DIR/core-analysis.json")
            else
                fail_test "Invalid JSON output"
                return
            fi
        else
            # Fallback parsing without jq
            local confidence=$(grep -o '"confidence_score": [0-9]*' "$TEST_OUTPUT_DIR/core-analysis.json" | cut -d' ' -f2)
            local total_checks=$(grep -o '"total_checks": [0-9]*' "$TEST_OUTPUT_DIR/core-analysis.json" | cut -d' ' -f2)
            local passed_checks=$(grep -o '"passed_checks": [0-9]*' "$TEST_OUTPUT_DIR/core-analysis.json" | cut -d' ' -f2)
        fi

        log_info "Confidence: $confidence%, Checks: $passed_checks/$total_checks"

        if [[ $confidence -ge 70 ]]; then
            pass_test
        else
            fail_test "Confidence score too low: $confidence%"
        fi
    else
        fail_test "Core analyzer failed to run"
    fi
}

# Test 2: Full analysis with artifact generation
test_full_analysis() {
    start_test "Full analysis with artifact generation"

    if "$SCRIPT_DIR/auto-analyze-milestone.sh" "docs/tech-specs/milestones/milestone-M0.1.mdx" --output-dir "$TEST_OUTPUT_DIR"; then
        # Check if all expected artifacts were generated
        local expected_files=(
            "$TEST_OUTPUT_DIR/validate-M0.1.mjs"
            "$TEST_OUTPUT_DIR/M0.1-analysis-summary.md"
            "$TEST_OUTPUT_DIR/agent-execution-guide-M0.1.md"
            "$TEST_OUTPUT_DIR/acceptance/M0.1-acceptance.sh"
        )

        local missing_files=()
        for file in "${expected_files[@]}"; do
            if [[ ! -f "$file" ]]; then
                missing_files+=("$(basename "$file")")
            fi
        done

        if [[ ${#missing_files[@]} -eq 0 ]]; then
            log_info "All artifacts generated successfully"
            pass_test
        else
            fail_test "Missing artifacts: ${missing_files[*]}"
        fi
    else
        fail_test "Full analysis script failed"
    fi
}

# Test 3: 70/30 principle validation
test_70_30_principle() {
    start_test "70/30 principle implementation"

    # Run analysis and check generated validation script
    if "$SCRIPT_DIR/auto-analyze-milestone.sh" "docs/tech-specs/milestones/milestone-M0.1.mdx" --output-dir "$TEST_OUTPUT_DIR" >/dev/null 2>&1; then
        local validation_script="$TEST_OUTPUT_DIR/validate-M0.1.mjs"

        if [[ -f "$validation_script" ]]; then
            # Check for 70% standard validations
            local standard_validations=(
                "validateProjectStructure"
                "validatePackageConfiguration"
                "validateDependencies"
            )

            # Check for 30% context-specific validations
            local context_validations=(
                "validateDomainExpertise"
                "validateProjectAdaptation"
                "validateExecutionFeedback"
            )

            local missing_standard=()
            local missing_context=()

            for validation in "${standard_validations[@]}"; do
                if ! grep -q "$validation" "$validation_script"; then
                    missing_standard+=("$validation")
                fi
            done

            for validation in "${context_validations[@]}"; do
                if ! grep -q "$validation" "$validation_script"; then
                    missing_context+=("$validation")
                fi
            done

            if [[ ${#missing_standard[@]} -eq 0 && ${#missing_context[@]} -eq 0 ]]; then
                log_info "70/30 principle correctly implemented"
                pass_test
            else
                fail_test "Missing validations - Standard: ${missing_standard[*]}, Context: ${missing_context[*]}"
            fi
        else
            fail_test "Validation script not generated"
        fi
    else
        fail_test "Analysis failed to run"
    fi
}

# Test 4: Generated validation script execution
test_generated_validation() {
    start_test "Generated validation script execution"

    # First generate the validation script
    if "$SCRIPT_DIR/auto-analyze-milestone.sh" "docs/tech-specs/milestones/milestone-M0.1.mdx" --output-dir "$TEST_OUTPUT_DIR" >/dev/null 2>&1; then
        local validation_script="$TEST_OUTPUT_DIR/validate-M0.1.mjs"

        if [[ -f "$validation_script" ]]; then
            # Try to run the generated validation script
            if node "$validation_script" > "$TEST_OUTPUT_DIR/validation-output.txt" 2>&1; then
                log_info "Generated validation script executed successfully"
                pass_test
            else
                local exit_code=$?
                log_warn "Validation script exited with code $exit_code (may be expected)"
                # Check if it's a validation failure vs script error
                if grep -q "Validation Summary" "$TEST_OUTPUT_DIR/validation-output.txt"; then
                    log_info "Script ran correctly but found validation issues"
                    pass_test
                else
                    fail_test "Script execution error"
                fi
            fi
        else
            fail_test "Validation script not found"
        fi
    else
        fail_test "Failed to generate validation script"
    fi
}

# Test 5: Multiple milestone confidence levels
test_multiple_milestones() {
    start_test "Multiple milestone confidence levels"

    # Test M0.1 (should have high confidence)
    log_info "Testing M0.1 (expected high confidence)..."
    if node "$SCRIPT_DIR/analyze-milestone-core.mjs" "docs/tech-specs/milestones/milestone-M0.1.mdx" --json > "$TEST_OUTPUT_DIR/m01-analysis.json" 2>/dev/null; then
        local m01_confidence
        if command -v jq >/dev/null 2>&1; then
            m01_confidence=$(jq -r '.confidence_score' "$TEST_OUTPUT_DIR/m01-analysis.json")
        else
            m01_confidence=$(grep -o '"confidence_score": [0-9]*' "$TEST_OUTPUT_DIR/m01-analysis.json" | cut -d' ' -f2)
        fi

        log_info "M0.1 confidence: $m01_confidence%"

        if [[ $m01_confidence -ge 90 ]]; then
            log_info "M0.1 shows high confidence as expected"
            pass_test
        else
            fail_test "M0.1 confidence too low: $m01_confidence%"
        fi
    else
        fail_test "Failed to analyze M0.1"
    fi
}

# Test 6: Configuration system
test_configuration_system() {
    start_test "Configuration system validation"

    local config_file="$SCRIPT_DIR/milestone-analysis-config.yml"

    if [[ -f "$config_file" ]]; then
        # Check for 70/30 principle in config
        if grep -q "70/30" "$config_file" && grep -q "standard_patterns" "$config_file" && grep -q "context_specific" "$config_file"; then
            log_info "Configuration file contains 70/30 principle"
            pass_test
        else
            fail_test "Configuration file missing 70/30 principle structure"
        fi
    else
        fail_test "Configuration file not found"
    fi
}

# Main test execution
main() {
    echo -e "${CYAN}🧪 Milestone Analysis System Test Suite${NC}"
    echo -e "${CYAN}=======================================${NC}"
    echo ""

    setup_test_environment

    # Run all tests
    test_core_analyzer
    test_full_analysis
    test_70_30_principle
    test_generated_validation
    test_multiple_milestones
    test_configuration_system

    # Test summary
    echo -e "${BLUE}📊 Test Summary${NC}"
    echo "==============="
    echo "Total Tests: $TOTAL_TESTS"
    echo "Passed: $PASSED_TESTS"
    echo "Failed: $FAILED_TESTS"

    if [[ $FAILED_TESTS -eq 0 ]]; then
        log_success "🎉 ALL TESTS PASSED!"
        cleanup_test_environment
        exit 0
    else
        log_error "❌ $FAILED_TESTS TEST(S) FAILED"
        log_info "Test artifacts preserved in $TEST_OUTPUT_DIR for debugging"
        exit 1
    fi
}

main "$@"
