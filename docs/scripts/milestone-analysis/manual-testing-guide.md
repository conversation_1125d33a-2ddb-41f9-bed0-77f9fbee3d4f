# Manual Testing Guide for 70/30 Milestone Analysis System

## Quick Testing Commands

### 1. **Basic Functionality Test**
```bash
# Test core analyzer
node docs/scripts/milestone-analysis/analyze-milestone-core.mjs docs/tech-specs/milestones/milestone-M0.1.mdx --json

# Expected: JSON output with confidence_score, total_checks, passed_checks
```

### 2. **Full Analysis Test**
```bash
# Test complete analysis pipeline
./docs/scripts/milestone-analysis/auto-analyze-milestone.sh docs/tech-specs/milestones/milestone-M0.1.mdx

# Expected: 
# - 95%+ confidence score
# - Generated validation script
# - Generated acceptance tests
# - Generated execution guide
# - Generated analysis report
```

### 3. **70/30 Principle Validation**
```bash
# Run analysis and check generated validation script
./docs/scripts/milestone-analysis/auto-analyze-milestone.sh docs/tech-specs/milestones/milestone-M0.1.mdx

# Check generated validation script contains:
grep -E "(validateProjectStructure|validatePackageConfiguration|validateDependencies)" docs/scripts/milestone-analysis/validate-M0.1.mjs
# Expected: 3 matches (70% standard validations)

grep -E "(validateDomainExpertise|validateProjectAdaptation|validateExecutionFeedback)" docs/scripts/milestone-analysis/validate-M0.1.mjs
# Expected: 3 matches (30% context-specific validations)
```

### 4. **Generated Script Execution Test**
```bash
# Test that generated validation script runs
node docs/scripts/milestone-analysis/validate-M0.1.mjs

# Expected: Script executes (may fail validation but shouldn't crash)
```

### 5. **Configuration System Test**
```bash
# Check 70/30 principle in configuration
grep -A 10 -B 5 "70/30" docs/scripts/milestone-analysis/milestone-analysis-config.yml

# Expected: Configuration shows 70/30 principle structure
```

### 6. **Demo System Test**
```bash
# Run the demo to see full system in action
./docs/scripts/milestone-analysis/demo-automated-analysis.sh

# Expected: Complete demonstration with M0.1 analysis
```

## Expected Results

### ✅ **M0.1 Milestone (Should Pass)**
- **Confidence Score**: 95%+ 
- **Status**: READY FOR EXECUTION
- **Generated Files**: 4 artifacts (validation, acceptance, guide, report)
- **70/30 Implementation**: All validation types present

### ⚠️ **M0.2 Milestone (May Have Lower Confidence)**
- **Confidence Score**: ~83% (below threshold)
- **Status**: NEEDS ATTENTION
- **Reason**: Different milestone structure, fewer success criteria
- **Expected Behavior**: System correctly identifies issues

### 🔧 **Generated Validation Script Structure**
```javascript
// 70% Standard Validations
validateProjectStructure()     // Check directories
validatePackageConfiguration() // Check package.json
validateDependencies()         // Check dependencies

// 30% Context-Specific Validations  
validateDomainExpertise()      // Knowledge graph, API, CI/CD specific
validateProjectAdaptation()    // Workspace patterns, tooling
validateExecutionFeedback()    // Analysis system integration
```

## Troubleshooting

### **Issue**: "Command not found" errors
**Solution**: Ensure you're in the repository root directory
```bash
cd /path/to/kloudi-swe-agent
```

### **Issue**: "Permission denied" errors  
**Solution**: Make scripts executable
```bash
chmod +x docs/scripts/milestone-analysis/*.sh
```

### **Issue**: JSON parsing errors
**Solution**: Install jq or use fallback parsing
```bash
# macOS
brew install jq

# The system has fallback parsing if jq is not available
```

### **Issue**: Low confidence scores
**Solution**: This is expected behavior for incomplete milestones
- M0.1: Should score 95%+ (well-structured)
- M0.2: May score lower (different structure)
- System correctly identifies quality issues

## Validation Checklist

- [ ] Core analyzer produces valid JSON output
- [ ] Full analysis generates 4 artifact files
- [ ] Generated validation script contains 6 validation methods
- [ ] 70% standard validations are present
- [ ] 30% context-specific validations are present  
- [ ] Configuration file shows 70/30 principle
- [ ] Demo script runs successfully
- [ ] High-quality milestones score 95%+
- [ ] Lower-quality milestones are correctly flagged

## Performance Expectations

| Test | Expected Time | Expected Result |
|------|---------------|-----------------|
| Core analyzer | < 2 seconds | JSON output |
| Full analysis | < 10 seconds | 4 generated files |
| Generated validation | < 5 seconds | Validation report |
| Demo script | < 30 seconds | Complete demonstration |

## Success Indicators

### 🟢 **System Working Correctly**
- M0.1 scores 95%+ confidence
- All 6 validation methods generated
- Scripts execute without crashes
- Configuration shows 70/30 structure

### 🟡 **Partial Success** 
- Some milestones score lower (expected)
- Scripts run but find validation issues
- Missing optional dependencies (jq)

### 🔴 **System Issues**
- Scripts crash with errors
- No files generated
- Invalid JSON output
- Missing validation methods

This manual testing approach provides comprehensive validation of the 70/30 principle implementation without requiring complex automated test infrastructure.
