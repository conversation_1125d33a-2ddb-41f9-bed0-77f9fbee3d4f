#!/usr/bin/env node

/**
 * Auto-generated validation script for M0.1
 * Generated by auto-analyze-milestone.sh
 *
 * This script provides 80% standard validation + 20% milestone-specific checks
 */

import { readFileSync, existsSync } from 'fs';

class M0_1Validator {
  constructor() {
    this.checks = 0;
    this.passed = 0;
    this.errors = [];
    this.warnings = [];
  }

  // 80% STANDARD VALIDATIONS - Common to all milestones
  validateProjectStructure() {
    console.log('📁 Validating project structure...');

    const requiredDirs = [
      'code/packages',
      'code/apps',
      'docs/tech-specs'
    ];

    requiredDirs.forEach(dir => {
      this.checks++;
      if (existsSync(dir)) {
        console.log(`✅ Directory exists: ${dir}`);
        this.passed++;
      } else {
        console.log(`❌ Missing directory: ${dir}`);
        this.errors.push(`Missing required directory: ${dir}`);
      }
    });
  }

  validatePackageConfiguration() {
    console.log('📦 Validating package configuration...');

    this.checks++;
    if (existsSync('code/package.json')) {
      console.log('✅ Main package.json exists');
      this.passed++;

      const pkg = JSON.parse(readFileSync('code/package.json', 'utf8'));

      // Check for required scripts based on milestone
      const requiredScripts = this.getRequiredScripts();
      requiredScripts.forEach(script => {
        this.checks++;
        if (pkg.scripts && pkg.scripts[script]) {
          console.log(`✅ Script exists: ${script}`);
          this.passed++;
        } else {
          console.log(`⚠️  Missing script: ${script}`);
          this.warnings.push(`Consider adding script: ${script}`);
        }
      });
    } else {
      console.log('❌ Main package.json missing');
      this.errors.push('Main package.json file not found');
    }
  }

  validateDependencies() {
    console.log('🔧 Validating dependencies...');

    const requiredDeps = this.getRequiredDependencies();

    if (existsSync('code/package.json')) {
      const pkg = JSON.parse(readFileSync('code/package.json', 'utf8'));
      const allDeps = { ...pkg.dependencies, ...pkg.devDependencies };

      requiredDeps.forEach(dep => {
        this.checks++;
        if (allDeps[dep]) {
          console.log(`✅ Dependency available: ${dep}`);
          this.passed++;
        } else {
          console.log(`⚠️  Missing dependency: ${dep}`);
          this.warnings.push(`Consider installing: ${dep}`);
        }
      });
    }
  }

  // 20% MILESTONE-SPECIFIC VALIDATIONS - Generated from milestone content
  validateMilestoneSpecificRequirements() {
    console.log('🎯 Validating milestone-specific requirements...');

    // Knowledge Graph specific validations
    this.checks++;
    if (existsSync('kg-schema.yml')) {
      console.log('✅ Knowledge graph schema exists');
      this.passed++;
    } else {
      console.log('⚠️  Missing kg-schema.yml');
      this.warnings.push('Knowledge graph schema file not found');
    }

    this.checks++;
    if (existsSync('code/packages/spec-parser-lib')) {
      console.log('✅ Spec parser library directory exists');
      this.passed++;
    } else {
      console.log('❌ Missing spec-parser-lib package');
      this.errors.push('Spec parser library package not found');
    }

    this.checks++;
    if (existsSync('code/packages/kg-cli')) {
      console.log('✅ Knowledge graph CLI directory exists');
      this.passed++;
    } else {
      console.log('❌ Missing kg-cli package');
      this.errors.push('Knowledge graph CLI package not found');
    }
  }

  getRequiredScripts() {
    return [
      'build',
      'test',
      'lint',
      'build-kg',
    ];
  }

  getRequiredDependencies() {
    return [
      'typescript',
      'gray-matter',
      'yaml',
      'uuid',
      'express',
    ];
  }

  generateReport() {
    console.log('\n📊 Validation Summary');
    console.log('===================');
    console.log(`Total checks: ${this.checks}`);
    console.log(`Passed: ${this.passed}`);
    console.log(`Errors: ${this.errors.length}`);
    console.log(`Warnings: ${this.warnings.length}`);

    const successRate = Math.round((this.passed / this.checks) * 100);
    console.log(`Success rate: ${successRate}%`);

    if (this.errors.length > 0) {
      console.log('\n🔴 Errors:');
      this.errors.forEach(error => console.log(`   • ${error}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n🟡 Warnings:');
      this.warnings.forEach(warning => console.log(`   • ${warning}`));
    }

    return successRate >= 90;
  }

  run() {
    console.log('🔍 Validating M0.1 milestone...');
    console.log('=====================================\n');

    this.validateProjectStructure();
    this.validatePackageConfiguration();
    this.validateDependencies();
    this.validateMilestoneSpecificRequirements();

    const success = this.generateReport();
    process.exit(success ? 0 : 1);
  }
}

const validator = new M0_1Validator();
validator.run();
