#!/usr/bin/env node

/**
 * Auto-generated validation script for M0
 * Generated by auto-analyze-milestone.sh
 * 
 * This script provides 80% standard validation + 20% milestone-specific checks
 */

import { readFileSync, existsSync } from 'fs';

class M0Validator {
  constructor() {
    this.checks = 0;
    this.passed = 0;
    this.errors = [];
    this.warnings = [];
  }

  // 80% STANDARD VALIDATIONS - Common to all milestones
  validateProjectStructure() {
    console.log('📁 Validating project structure...');
    
    const requiredDirs = [
      'apps',
      'apps/api',
      'apps/web',
      'packages',
      'packages/shared',
      'docs/tech-specs'
    ];
    
    requiredDirs.forEach(dir => {
      this.checks++;
      if (existsSync(dir)) {
        console.log(`✅ Directory exists: ${dir}`);
        this.passed++;
      } else {
        console.log(`❌ Missing directory: ${dir}`);
        this.errors.push(`Missing required directory: ${dir}`);
      }
    });
  }

  validatePackageConfiguration() {
    console.log('📦 Validating package configuration...');
    
    this.checks++;
    if (existsSync('package.json')) {
      console.log('✅ Main package.json exists');
      this.passed++;
      
      const pkg = JSON.parse(readFileSync('package.json', 'utf8'));
      
      // Check for required scripts based on milestone
      const requiredScripts = this.getRequiredScripts();
      requiredScripts.forEach(script => {
        this.checks++;
        if (pkg.scripts && pkg.scripts[script]) {
          console.log(`✅ Script exists: ${script}`);
          this.passed++;
        } else {
          console.log(`⚠️  Missing script: ${script}`);
          this.warnings.push(`Consider adding script: ${script}`);
        }
      });
    } else {
      console.log('❌ Main package.json missing');
      this.errors.push('Main package.json file not found');
    }
  }

  validateDependencies() {
    console.log('🔧 Validating dependencies...');
    
    const requiredDeps = this.getRequiredDependencies();
    
    if (existsSync('package.json')) {
      const pkg = JSON.parse(readFileSync('package.json', 'utf8'));
      const allDeps = { ...pkg.dependencies, ...pkg.devDependencies };
      
      requiredDeps.forEach(dep => {
        this.checks++;
        if (allDeps[dep]) {
          console.log(`✅ Dependency available: ${dep}`);
          this.passed++;
        } else {
          console.log(`⚠️  Missing dependency: ${dep}`);
          this.warnings.push(`Consider installing: ${dep}`);
        }
      });
    }
  }

  // 20% MILESTONE-SPECIFIC VALIDATIONS - Generated from milestone content
  validateMilestoneSpecificRequirements() {
    console.log('🎯 Validating milestone-specific requirements...');
    
    // Infrastructure specific validations
    this.checks++;
    if (existsSync('pnpm-workspace.yaml')) {
      console.log('✅ pnpm workspace configuration exists');
      this.passed++;
    } else {
      console.log('❌ Missing pnpm-workspace.yaml');
      this.errors.push('pnpm workspace configuration not found');
    }
    
    this.checks++;
    if (existsSync('.github/workflows/ci.yml')) {
      console.log('✅ GitHub Actions CI workflow exists');
      this.passed++;
    } else {
      console.log('❌ Missing GitHub Actions CI workflow');
      this.errors.push('CI workflow not found');
    }
    
    this.checks++;
    if (existsSync('docker-compose.yml')) {
      console.log('✅ Docker compose configuration exists');
      this.passed++;
    } else {
      console.log('❌ Missing docker-compose.yml');
      this.errors.push('Docker compose configuration not found');
    }
    
    this.checks++;
    if (existsSync('scripts/m0-acceptance.sh')) {
      console.log('✅ M0 acceptance test script exists');
      this.passed++;
    } else {
      console.log('❌ Missing M0 acceptance test script');
      this.errors.push('M0 acceptance test script not found');
    }
    
    // Check for API health endpoint
    this.checks++;
    if (existsSync('apps/api/src/index.ts')) {
      const apiContent = readFileSync('apps/api/src/index.ts', 'utf8');
      if (apiContent.includes('/health')) {
        console.log('✅ API health endpoint implemented');
        this.passed++;
      } else {
        console.log('⚠️  API health endpoint not found');
        this.warnings.push('API should include /health endpoint');
      }
    } else {
      console.log('⚠️  API index file not found');
      this.warnings.push('API implementation missing');
    }
    
    // Check for shared Result type
    this.checks++;
    if (existsSync('packages/shared/Result.ts')) {
      console.log('✅ Shared Result type exists');
      this.passed++;
    } else {
      console.log('⚠️  Shared Result type missing');
      this.warnings.push('Shared Result type should be implemented');
    }
  }

  getRequiredScripts() {
    return [
      'build',
      'test',
      'lint',
      'dev:api',
      'dev:web',
      'test:coverage',
      'type-check'
    ];
  }

  getRequiredDependencies() {
    return [
      'typescript',
      'express',
      'react',
      'jest',
      'vitest',
      'eslint',
      'prettier',
      'tsup',
      'turbo'
    ];
  }

  generateReport() {
    console.log('\n📊 Validation Summary');
    console.log('===================');
    console.log(`Total checks: ${this.checks}`);
    console.log(`Passed: ${this.passed}`);
    console.log(`Errors: ${this.errors.length}`);
    console.log(`Warnings: ${this.warnings.length}`);
    
    const successRate = Math.round((this.passed / this.checks) * 100);
    console.log(`Success rate: ${successRate}%`);
    
    if (this.errors.length > 0) {
      console.log('\n🔴 Errors:');
      this.errors.forEach(error => console.log(`   • ${error}`));
    }
    
    if (this.warnings.length > 0) {
      console.log('\n🟡 Warnings:');
      this.warnings.forEach(warning => console.log(`   • ${warning}`));
    }
    
    if (successRate >= 90) {
      console.log('\n🎉 M0 milestone validation passed!');
      console.log('✅ Repository skeleton is ready or well-implemented');
    } else {
      console.log('\n⚠️  M0 milestone needs attention');
      console.log('🔧 Review errors and implement missing components');
    }
    
    return successRate >= 90;
  }

  run() {
    console.log('🔍 Validating M0 milestone...');
    console.log('=====================================\n');
    
    this.validateProjectStructure();
    this.validatePackageConfiguration();
    this.validateDependencies();
    this.validateMilestoneSpecificRequirements();
    
    const success = this.generateReport();
    process.exit(success ? 0 : 1);
  }
}

const validator = new M0Validator();
validator.run();
