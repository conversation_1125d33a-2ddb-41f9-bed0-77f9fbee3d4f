#!/usr/bin/env node

/**
 * Spec Lint Script - Agent Optimized
 *
 * Validates milestone specification files with clear, unambiguous requirements.
 * Designed for automated agent usage with predictable validation rules.
 */

import { readFileSync } from "fs";
import { resolve } from "path";

// Import gray-matter from the code directory where it's installed
let matter;
try {
  const { createRequire } = await import("module");
  const require = createRequire(import.meta.url);
  matter = require("../code/node_modules/gray-matter");
} catch (error) {
  console.error(
    "❌ gray-matter dependency not found. Please run: cd code && pnpm install"
  );
  process.exit(1);
}

// Required frontmatter fields
const requiredFrontmatter = [
  "title",
  "description",
  "created",
  "version",
  "status",
  "tags"
];

// Optional frontmatter fields (show warnings only)
const optionalFrontmatter = ["updated", "authors"];

// Required sections - exact matches for agent clarity
const requiredSections = [
  "## 🧳 Toolchain Versions",
  "## 🎯 Definition of Done",
  "## 📦 Deliverables",
  "## 🗂 Directory Layout",
  "## 🧠 Key Decisions",
  "## ✅ Success Criteria",
  "## 🔨 Task Breakdown",
  "## 🤖 CI Pipeline",
  "## 🧪 Acceptance Tests"
];

const validStatuses = [
  "Draft",
  "Approved",
  "In Progress",
  "In Review",
  "Completed"
];
const versionRegex = /^\d+\.\d+\.\d+$/;

// Main execution
const specFile = process.argv[2];

if (!specFile) {
  console.error("Usage: node scripts/spec-lint.mjs <spec-file>");
  process.exit(1);
}

console.log(`🔍 Linting specification: ${specFile}`);

try {
  const content = readFileSync(resolve(specFile), "utf8");
  const { data: frontmatter, content: body } = matter(content);

  let frontmatterErrors = 0;
  let sectionErrors = 0;
  let validationErrors = 0;

  // Check required frontmatter
  console.log("\n📋 Checking frontmatter...");
  for (const field of requiredFrontmatter) {
    if (!(field in frontmatter)) {
      console.log(`❌ Missing frontmatter field: ${field}`);
      frontmatterErrors++;
    } else {
      console.log(`✅ Found: ${field}`);
    }
  }

  // Check optional frontmatter
  for (const field of optionalFrontmatter) {
    if (!(field in frontmatter)) {
      console.log(`⚠️  Optional field missing: ${field}`);
    } else {
      console.log(`✅ Found: ${field}`);
    }
  }

  // Check required sections
  console.log("\n📑 Checking required sections...");
  for (const section of requiredSections) {
    if (
      !new RegExp(
        `^${section.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}`,
        "m"
      ).test(body)
    ) {
      console.log(`❌ Missing section: ${section}`);
      sectionErrors++;
    } else {
      console.log(`✅ Found: ${section}`);
    }
  }

  // Check status
  console.log("\n📊 Checking status...");
  if (!validStatuses.includes(frontmatter.status)) {
    console.log(
      `❌ Invalid status: ${
        frontmatter.status
      }. Must be one of: ${validStatuses.join(", ")}`
    );
    validationErrors++;
  } else {
    console.log(`✅ Valid status: ${frontmatter.status}`);
  }

  // Check version format
  console.log("\n🔢 Checking version format...");
  if (!versionRegex.test(frontmatter.version)) {
    console.log(
      `❌ Invalid version format: ${frontmatter.version}. Must be semver (x.y.z)`
    );
    validationErrors++;
  } else {
    console.log(`✅ Valid version: ${frontmatter.version}`);
  }

  // Calculate total errors and exit with appropriate code
  const totalErrors = frontmatterErrors + sectionErrors + validationErrors;

  console.log("\n📊 Lint Summary:");
  if (totalErrors === 0) {
    console.log("✅ Spec passed lint");
    process.exit(0);
  } else {
    console.log(`❌ ${totalErrors} error(s) found:`);
    if (frontmatterErrors > 0) {
      console.log(`   - ${frontmatterErrors} frontmatter error(s)`);
    }
    if (sectionErrors > 0) {
      console.log(`   - ${sectionErrors} section error(s)`);
    }
    if (validationErrors > 0) {
      console.log(`   - ${validationErrors} validation error(s)`);
    }
    console.log("🔧 Please fix the issues above");

    // Exit with specific codes
    if (frontmatterErrors > 0) {
      process.exit(2); // Frontmatter errors
    } else if (sectionErrors > 0) {
      process.exit(3); // Section errors
    } else {
      process.exit(1); // Other validation errors
    }
  }
} catch (error) {
  console.error(`❌ Error reading spec file: ${error.message}`);
  process.exit(1);
}
